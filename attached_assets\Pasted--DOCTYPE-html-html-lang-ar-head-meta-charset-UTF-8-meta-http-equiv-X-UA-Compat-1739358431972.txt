<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="../favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <title>محطات نور خوي</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.2.0/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.0/xlsx.full.min.js"></script>
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAuoser5drR-xx8k9GzGz3d8FZNBY60_LE&libraries=geometry&callback=initMap">
    </script>
    <script async src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAuoser5drR-xx8k9GzGz3d8FZNBY60_LE&libraries=geometry&callback=initMap"></script>
    <script type="module" src="script.js"></script>
    <script src="map.js" defer></script>
</head>
    <!-- نافذة الرسالة المخصصة -->
    <div id="customAlert" class="custom-alert">
        <div class="alert-content">
            <span class="close-btn" onclick="closeCustomAlert()">&times;</span>
            <h3 id="alertTitle"></h3>
            <p id="alertMessage"></p>
            <p class="welcome-message">نتشرف بتواجدك</p>
        </div>
    </div>
<body class="body">
    <div id="loadingMessage" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.7); color: white; padding: 20px; border-radius: 10px; z-index: 1000;">جاري التحميل...</div>
    <!-- زر فتح/إغلاق القائمة -->
    <button id="toggleSidebar" class="toggle-sidebar"><i class="fas fa-bars"></i></button>
    <!-- القائمة الجانبية -->
    <nav class="sidebar">
        <ul>
            <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
            <li><a href="station.html"><i class="fas fa-gas-pump"></i> المحطات</a></li>
            <li><a href="contact.html"><i class="fas fa-phone"></i> اتصل بنا</a></li>
        </ul>
    </nav>


    <header class="header">
        <img src="../logo1.png" alt="شعار" class="logo">
        <h1>محطات نور خوي</h1>
            <!-- زر تغيير اللغة -->
    <button id="languageToggle" onclick="toggleLanguage()">
        <i class="fas fa-globe"></i>
    </button>
    </header>
    
    <div class="map-container" id="map"></div>

    <div class="main-content">
        <div class="select-container">
            <div>
                <label for="regionSelect"></label>
                <select id="regionSelect">
                    <option value="">اختر منطقة</option>
                </select>
            </div>
            <div>
                <label for="startLocation"></label>
                <select id="startLocation" disabled>
                    <option value="موقعي الحالي"></option>
                </select>
            </div>
            <div>
                <label for="endLocation"></label>
                <select id="endLocation" disabled>
                    <option value="موقعي الحالي"></option>
                </select>
            </div>
            <div>
                <button onclick="resetAll()">إعادة التعيين</button>
            </div>
            <!-- زر عرض اقرب محطة -->
            <div>
                <button id="nearestStationButton" onclick="findNearestStation()">عرض اقرب محطة</button>
            </div>
        </div>
    </div>

    <div class="table">
        <!-- قسم المناطق والمواقع -->
        <div class="table-container">
            <h4>المناطق والمواقع</h4>
            <table>
                <thead>
                    <tr>
                        <th>المنطقة</th>
                        <th>الموقع</th>
                        <th>المسافة المتوسطة (كم)</th>
                        <th>الخريطة</th>
                    </tr>
                </thead>
                <tbody id="regionsTable"></tbody>
            </table>
        </div>
    
        <!-- قسم حساب المسافة -->
        <div class="table-container">
            <h4>حساب المسافة بين موقعين</h4>
            <table>
                <thead>
                    <tr>
                        <th>الموقع الأول</th>
                        <th>الموقع الثاني</th>
                        <th>المسافة (كم)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td id="selectedStart">---</td>
                        <td id="selectedEnd">---</td>
                        <td id="calculatedDistance">---</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <footer class="footer">
        <p class="footer-title">Copyrights @ <span>Noor Khoy</span> <span id="currentYear"></span></p>
    </footer>
</body>
</html>