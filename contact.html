<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="../favicon.png" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="style5.css">
    <link rel="stylesheet" href="style11.css">
    <title>اتصل بنا - محطات نور خوي</title>
    <style>
        /* تعديل لإظهار الزر في المنتصف */
        .form {
            margin-top: 50px;
            display: flex;
            flex-direction: column;
            align-items: center; /* محاذاة العناصر في المنتصف */
        }


    </style>
</head>
<body class="body">
    <!-- زر فتح/إغلاق القائمة -->
    <button id="toggleSidebar" class="toggle-sidebar"><i class="fas fa-bars"></i></button>

    <!-- القائمة الجانبية -->
    <nav class="sidebar">
        <ul>
            <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
            <li><a href="station.html"><i class="fas fa-gas-pump"></i> المحطات</a></li>
            <li><a href="contact.html"><i class="fas fa-phone"></i> اتصل بنا</a></li>
        </ul>
    </nav>

    <header class="header">
        <img src="../logo1.png" alt="شعار" class="logo">
        <h1>محطات نور خوي</h1>
    </header>

    <div class="contact-container">
        <div class="heading">تواصل معنا</div>
        <form id="contactForm" class="form">
            <div class="form__group field">
                <input type="input" class="form__field" placeholder="الاسم" id="name" required="">
                <label for="name" class="form__label">الاسم</label>
            </div>
            
            <div class="form__group field">
                <input type="email" class="form__field" placeholder="البريد الإلكتروني" id="email" name="email" required>
                <label for="email" class="form__label">البريد الإلكتروني</label>
            </div>
            
            <div class="form__group field">
                <input type="tel" class="form__field" placeholder="رقم الهاتف" id="phone" name="phone" required>
                <label for="phone" class="form__label">رقم الهاتف</label>
            </div>
            
            <div class="form__group field">
                <textarea class="form__field" placeholder="الرسالة" id="message" name="message" rows="5" required></textarea>
                <label for="message" class="form__label">الرسالة</label>
            </div>

            <button class="button">
                <div class="outline"></div>
                <div class="state state--default">
                    <div class="icon">
                        <svg width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g style="filter: url(#shadow)">
                                <path d="M14.2199 21.63C13.0399 21.63 11.3699 20.8 10.0499 16.83L9.32988 14.67L7.16988 13.95C3.20988 12.63 2.37988 10.96 2.37988 9.78001C2.37988 8.61001 3.20988 6.93001 7.16988 5.60001L15.6599 2.77001C17.7799 2.06001 19.5499 2.27001 20.6399 3.35001C21.7299 4.43001 21.9399 6.21001 21.2299 8.33001L18.3999 16.82C17.0699 20.8 15.3999 21.63 14.2199 21.63ZM7.63988 7.03001C4.85988 7.96001 3.86988 9.06001 3.86988 9.78001C3.86988 10.5 4.85988 11.6 7.63988 12.52L10.1599 13.36C10.3799 13.43 10.5599 13.61 10.6299 13.83L11.4699 16.35C12.3899 19.13 13.4999 20.12 14.2199 20.12C14.9399 20.12 16.0399 19.13 16.9699 16.35L19.7999 7.86001C20.3099 6.32001 20.2199 5.06001 19.5699 4.41001C18.9199 3.76001 17.6599 3.68001 16.1299 4.19001L7.63988 7.03001Z" fill="currentColor"></path>
                                <path d="M10.11 14.4C9.92005 14.4 9.73005 14.33 9.58005 14.18C9.29005 13.89 9.29005 13.41 9.58005 13.12L13.16 9.53C13.45 9.24 13.93 9.24 14.22 9.53C14.51 9.82 14.51 10.3 14.22 10.59L10.64 14.18C10.5 14.33 10.3 14.4 10.11 14.4Z" fill="currentColor"></path>
                            </g>
                            <defs>
                                <filter id="shadow">
                                    <fedropshadow dx="0" dy="1" stdDeviation="0.6" flood-opacity="0.5"></fedropshadow>
                                </filter>
                            </defs>
                        </svg>
                    </div>
                    <p>
                        <span style="--i:0">S</span>
                        <span style="--i:1">e</span>
                        <span style="--i:2">n</span>
                        <span style="--i:3">d</span>
                        <span style="--i:4">M</span>
                        <span style="--i:5">e</span>
                        <span style="--i:6">s</span>
                        <span style="--i:7">s</span>
                        <span style="--i:8">a</span>
                        <span style="--i:9">g</span>
                        <span style="--i:10">e</span>
                    </p>
                </div>
            </button>
            
            <div class="contact-info">
                <span class="title">معلومات التواصل</span>
                <ul>
                    <li><i class="fas fa-phone"></i><a href="tel:+9668003033313" class="phone-link">8003033313</a> هاتف</li>
                    <li><i class="fas fa-envelope"></i> <a href="mailto:<EMAIL>" class="email-link">   <EMAIL></a></li>
                    <li><i class="fas fa-map-marker-alt"> <a href="https://maps.app.goo.gl/Lx6bJuouAS5Pmys59"class="maps-link"></a></i> العنوان: نجران، العريسة، المملكة العربية السعودية</li>
                </ul>
            </div>
        </form>
    </div>

    <footer class="footer">
        <p class="footer-title">Copyrights @ <span>Noor Khoy</span> <span id="currentYear"></span></p>
    </footer>

    <script src="script5.js"></script>
</body>
</html>
