<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تهنئة بشهر رمضان الكريم</title>
    <style>
        /* الحفاظ على كافة الأنماط السابقة مع إضافة تحسينات */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }

        body {
            background: url('https://img.freepik.com/premium-photo/silver-lamp-with-moon-words-ramadanislamic-ramadan-greeting-background_430468-743.jpg?w=996') no-repeat center center fixed;
            background-size: cover;
            color: #fff;
        }
        /* الشعار */
        .logo {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 50px;
            height: 40px;
            z-index: 1001;
        }
        /* تحسينات الصوت والراديو */
        .quran-radio {
            margin: 20px auto;
            padding: 15px;
            background: rgba(0, 0, 0, 0.548);
            border-radius: 10px;
            max-width: 500px;
        }
        .quran-radio h3 {
            color: #ffcc00;
            margin-bottom: 15px;
            font-size: 24px;
        }

        #quranPlayer {
            width: 100%;
            margin-top: 10px;
        }

        .surah-selector {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            background: #333;
            color: #fff;
            border: 1px solid #ffcc00;
            border-radius: 5px;
        }

        .audio-controls button {
            background: #ffcc00;
            color: #000;
            border: none;
            padding: 8px 20px;
            margin: 5px;
            border-radius: 20px;
            cursor: pointer;
            transition: 0.3s;
        }

        .audio-controls button:hover {
            background: #ffd700;
        }
        /* أنماط البوب أب */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }

        .modal-content {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            max-width: 90%;
            max-height: 80vh;
            overflow: auto;
            box-shadow: 0 0 20px rgba(255, 204, 0, 0.5);
        }

        .close-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            font-size: 24px;
            color: #ff0000;
            cursor: pointer;
        }

        /* تحسينات الجدول */
        .modal-content table {
            width: 100%;
            border-collapse: collapse;
            background: #f8f8f8;
            color: #333;
        }

        .modal-content th, 
        .modal-content td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: center;
        }

        .modal-content th {
            background-color: #ffcc00;
            color: #000;
        }

        .modal-content tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .time-bar {
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            text-align: center;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .time-bar span {
            margin: 0 10px;
            font-size: 20px;
            text-shadow: 0 0 5px #141414;
        }

        .header-content {
            text-align: center;
            padding-top: 58px;
            margin-bottom: 14px;
        }

        h1 {
            font-size: 42px;
            color: #ff7300;
            text-shadow: 0 0 10px #000000;
        }
        header p {
            font-size: 25px;
            text-align: center;
            color: #141414;
            margin-top: 10px;
            text-shadow: 0 0 10px #000000;
        }
        .container {
            max-width:  650px;
            margin: 10px auto;
            border: 2px solid #00000011;
            box-shadow: 0 0 20px rgb(0, 0, 0);
            padding: 20px;
            background: rgba(0, 0, 0, 0);
            border-radius: 15px;
            text-align: center;
        }
        .content h2 {
            font-size: 32px;
            color: #ffffff;
            text-shadow: 0 0 10px #2c2727;
        }

        .content p {
            font-size: 22px;
            margin-bottom: 20px;
            color: #000000;
            text-shadow: 0 0 5px #000000;
        }
        .slider {
            position: relative;
            width: 100%;
            height: 300px;
            overflow: hidden;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.7);
        }

        .slides {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .slides img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 15px;
            opacity: 0;
            transition: opacity 5s ease-in-out;
        }

        @keyframes fade {
            0% { opacity: 1; }
            33% { opacity: 1; }
            50% { opacity: 0; }
            100% { opacity: 0; }
        }

        .slides img:nth-child(1) { animation: fade 15s infinite 0s; }
        .slides img:nth-child(2) { animation: fade 15s infinite 5s; }
        .slides img:nth-child(3) { animation: fade 15s infinite 10s; }
        .slides img:nth-child(4) { animation: fade 15s infinite 15s; }
        .slides img:nth-child(5) { animation: fade 15s infinite 20s; }

        .lanterns {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 40px 0;
        }

        .toggle-btn {
            background: #ffcc00;
            color: #000;
            padding: 12px 40px;
            border: none;
            border-radius: 30px;
            font-size: 20px;
            cursor: pointer;
            margin: 25px 0;
            transition: all 0.3s ease;
            box-shadow: 0 0 15px rgba(255, 204, 0, 0.5);
        }

        .toggle-btn:hover {
            transform: scale(1.05);
            background: #ffd700;
        }

        .imsakiyah-table {
            max-height: 0;
            overflow: auto;
            transition: 0.5s ease-in-out;
            width: 100%;
            margin: 0 auto;
        }

        .imsakiyah-table.active {
            max-height: 600px;
            padding: 15px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: rgba(0, 0, 0, 0.8);
            min-width: 800px;
        }

        th, td {
            padding: 12px;
            border: 1px solid #ffcc00;
            font-size: 14px;
        }

        th {
            background: rgba(255, 204, 0, 0.2);
            font-size: 16px;
        }

        .lanterns {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
        }

        .lantern-img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.7);
        }

        footer {
            font-size: 16px;
            color: #000000;
            margin-top: 30px;
            text-shadow: 0 0 10px #000000;
        }

        /* استعلامات الوسائط للهواتف المحمولة */
        @media (max-width: 768px) {
            h1 {
                font-size: 32px;
            }

            header p {
                font-size: 18px;
            }

            .container {
                padding: 10px;
            }

            .content h2 {
                font-size: 24px;
            }

            .content p {
                font-size: 16px;
            }

            .slider {
                height: 200px;
            }

            .time-bar span {
                font-size: 16px;
            }

            .quran-radio h3 {
                font-size: 20px;
            }

            .toggle-btn {
                padding: 10px 20px;
                font-size: 16px;
            }

            .modal-content {
                max-width: 95%;
                max-height: 90vh;
            }

            .modal-content table {
                min-width: 100%;
            }

            .modal-content th, 
            .modal-content td {
                padding: 8px;
                font-size: 12px;
            }

            .lantern-img {
                width: 80px;
                height: 80px;
            }
        }

        @media (max-width: 480px) {
            h1 {
                font-size: 28px;
            }

            header p {
                font-size: 16px;
            }

            .content h2 {
                font-size: 20px;
            }

            .content p {
                font-size: 14px;
            }

            .slider {
                height: 150px;
            }

            .time-bar span {
                font-size: 14px;
            }

            .quran-radio h3 {
                font-size: 18px;
            }

            .toggle-btn {
                padding: 8px 16px;
                font-size: 14px;
            }

            .modal-content th, 
            .modal-content td {
                padding: 6px;
                font-size: 10px;
            }

            .lantern-img {
                width: 60px;
                height: 60px;
            }
        }
    </style>
</head>
<body>

    <!-- إضافة الصوت التلقائي المعدل -->
    <audio id="welcomeSound">
        <source src="https://github.com/karim-fayed/station-Noor/raw/main/data/Ramadan.mp3" type="audio/mpeg">
        Your browser does not support the audio element.
    </audio>

    <!-- شريط التوقيت -->
    <div class="time-bar" id="timeBar">
            <!-- الشعار -->
    <img src="https://www2.0zz0.com/2025/02/27/13/598253117.png" alt="شعار رمضان" class="logo">
        <span id="gregorianDate"></span>
        <span id="hijriDate"></span>
        <span id="liveClock"></span>
    </div>

    <header class="header-content">
        <h1>رمضان كريم</h1>
        <p>كل عام وأنتم بخير بمناسبة شهر رمضان المبارك</p>
    </header>

    <div class="container">
        <section class="content">
            <h2>مبارك عليكم الشهر الفضيل</h2>
            <p>نسأل الله أن يتقبل منا ومنكم صالح الأعمال وأن يبارك في أيام هذا الشهر الكريم.</p>
            <!-- السلايدر -->
            <div class="slider">
                <div class="slides">
                    <img src="https://www2.0zz0.com/2025/02/27/13/598253117.png" alt="صورة 01">
                    <img src="https://www2.0zz0.com/2025/02/27/14/501407098.png" alt="صورة 02">
                    <img src="https://images.pexels.com/photos/2233416/pexels-photo-2233416.jpeg" alt="صورة 1">
                    <img src="https://media.licdn.com/dms/image/v2/C5622AQESeyTMfVWx6Q/feedshare-shrink_2048_1536/feedshare-shrink_2048_1536/0/1679565630035?e=2147483647&v=beta&t=xaSPDD7FyxXD4Hm3fQsMRwRtThby7herak6R1dbxiGQ" alt="صورة 2">
                    <img src="https://cdn.salla.sa/dyYK/NDa2JypSn7imFW71YdmIkhymgEnQlyTrqrBLQ8R2.jpg" alt="صورة 3">

                </div>
            </div>
        </section>
        <!-- الفوانيس 
        <div class="lanterns">
            <img src="https://images.pexels.com/photos/7772490/pexels-photo-7772490.jpeg" class="lantern-img">
            <img src="https://images.pexels.com/photos/7794399/pexels-photo-7794399.jpeg" class="lantern-img">
            <img src="https://images.pexels.com/photos/5725955/pexels-photo-5725955.jpeg" class="lantern-img">
        </div> -->

        <!-- زر الإمساكية -->
    <!-- زر الإمساكية -->
    <button class="toggle-btn" onclick="showImsakiyah()">إظهار إمساكية رمضان ⏳</button>
    <div class="quran-radio">
        <h3>📻 القرآن الكريم</h3>
        <select class="surah-selector" id="surahSelector">
            <option value="">اختر سورة للاستماع</option>
            <option value="https://server11.mp3quran.net/sds/001.mp3">سورة الفاتحة</option>
            <option value="https://server11.mp3quran.net/sds/002.mp3">سورة البقرة</option>
            <option value="https://server11.mp3quran.net/sds/003.mp3">سورة آل عمران</option>
            <option value="https://server11.mp3quran.net/sds/004.mp3">سورة النساء</option>
            <option value="https://server11.mp3quran.net/sds/005.mp3">سورة المائدة</option>
            <option value="https://server11.mp3quran.net/sds/006.mp3">سورة الأنعام</option>
            <option value="https://server11.mp3quran.net/sds/007.mp3">سورة الأعراف</option>
            <option value="https://server11.mp3quran.net/sds/008.mp3">سورة الأنفال</option>
            <option value="https://server11.mp3quran.net/sds/009.mp3">سورة التوبة</option>
            <option value="https://server11.mp3quran.net/sds/010.mp3">سورة يونس</option>
            <option value="https://server11.mp3quran.net/sds/011.mp3">سورة هود</option>
            <option value="https://server11.mp3quran.net/sds/012.mp3">سورة يوسف</option>
            <option value="https://server11.mp3quran.net/sds/013.mp3">سورة الرعد</option>
            <option value="https://server11.mp3quran.net/sds/014.mp3">سورة إبراهيم</option>
            <option value="https://server11.mp3quran.net/sds/015.mp3">سورة الحجر</option>
            <option value="https://server11.mp3quran.net/sds/016.mp3">سورة النحل</option>
            <option value="https://server11.mp3quran.net/sds/017.mp3">سورة الإسراء</option>
            <option value="https://server11.mp3quran.net/sds/018.mp3">سورة الكهف</option>
            <option value="https://server11.mp3quran.net/sds/019.mp3">سورة مريم</option>
            <option value="https://server11.mp3quran.net/sds/020.mp3">سورة طه</option>
            <option value="https://server11.mp3quran.net/sds/021.mp3">سورة الأنبياء</option>
            <option value="https://server11.mp3quran.net/sds/022.mp3">سورة الحج</option>
            <option value="https://server11.mp3quran.net/sds/023.mp3">سورة المؤمنون</option>
            <option value="https://server11.mp3quran.net/sds/024.mp3">سورة النور</option>
            <option value="https://server11.mp3quran.net/sds/025.mp3">سورة الفرقان</option>
            <option value="https://server11.mp3quran.net/sds/026.mp3">سورة الشعراء</option>
            <option value="https://server11.mp3quran.net/sds/027.mp3">سورة النمل</option>
            <option value="https://server11.mp3quran.net/sds/028.mp3">سورة القصص</option>
            <option value="https://server11.mp3quran.net/sds/029.mp3">سورة العنكبوت</option>
            <option value="https://server11.mp3quran.net/sds/030.mp3">سورة الروم</option>
            <option value="https://server11.mp3quran.net/sds/031.mp3">سورة لقمان</option>
            <option value="https://server11.mp3quran.net/sds/032.mp3">سورة السجدة</option>
            <option value="https://server11.mp3quran.net/sds/033.mp3">سورة الأحزاب</option>
            <option value="https://server11.mp3quran.net/sds/034.mp3">سورة سبأ</option>
            <option value="https://server11.mp3quran.net/sds/035.mp3">سورة فاطر</option>
            <option value="https://server11.mp3quran.net/sds/036.mp3">سورة يس</option>
            <option value="https://server11.mp3quran.net/sds/037.mp3">سورة الصافات</option>
            <option value="https://server11.mp3quran.net/sds/038.mp3">سورة ص</option>
            <option value="https://server11.mp3quran.net/sds/039.mp3">سورة الزمر</option>
            <option value="https://server11.mp3quran.net/sds/040.mp3">سورة غافر</option>
            <option value="https://server11.mp3quran.net/sds/041.mp3">سورة فصلت</option>
            <option value="https://server11.mp3quran.net/sds/042.mp3">سورة الشورى</option>
            <option value="https://server11.mp3quran.net/sds/043.mp3">سورة الزخرف</option>
            <option value="https://server11.mp3quran.net/sds/044.mp3">سورة الدخان</option>
            <option value="https://server11.mp3quran.net/sds/045.mp3">سورة الجاثية</option>
            <option value="https://server11.mp3quran.net/sds/046.mp3">سورة الأحقاف</option>
            <option value="https://server11.mp3quran.net/sds/047.mp3">سورة محمد</option>
            <option value="https://server11.mp3quran.net/sds/048.mp3">سورة الفتح</option>
            <option value="https://server11.mp3quran.net/sds/049.mp3">سورة الحجرات</option>
            <option value="https://server11.mp3quran.net/sds/050.mp3">سورة ق</option>
            <option value="https://server11.mp3quran.net/sds/051.mp3">سورة الذاريات</option>
            <option value="https://server11.mp3quran.net/sds/052.mp3">سورة الطور</option>
            <option value="https://server11.mp3quran.net/sds/053.mp3">سورة النجم</option>
            <option value="https://server11.mp3quran.net/sds/054.mp3">سورة القمر</option>
            <option value="https://server11.mp3quran.net/sds/055.mp3">سورة الرحمن</option>
            <option value="https://server11.mp3quran.net/sds/056.mp3">سورة الواقعة</option>
            <option value="https://server11.mp3quran.net/sds/057.mp3">سورة الحديد</option>
            <option value="https://server11.mp3quran.net/sds/058.mp3">سورة المجادلة</option>
            <option value="https://server11.mp3quran.net/sds/059.mp3">سورة الحشر</option>
            <option value="https://server11.mp3quran.net/sds/060.mp3">سورة الممتحنة</option>
            <option value="https://server11.mp3quran.net/sds/061.mp3">سورة الصف</option>
            <option value="https://server11.mp3quran.net/sds/062.mp3">سورة الجمعة</option>
            <option value="https://server11.mp3quran.net/sds/063.mp3">سورة المنافقون</option>
            <option value="https://server11.mp3quran.net/sds/064.mp3">سورة التغابن</option>
            <option value="https://server11.mp3quran.net/sds/065.mp3">سورة الطلاق</option>
            <option value="https://server11.mp3quran.net/sds/066.mp3">سورة التحريم</option>
            <option value="https://server11.mp3quran.net/sds/067.mp3">سورة الملك</option>
            <option value="https://server11.mp3quran.net/sds/068.mp3">سورة القلم</option>
            <option value="https://server11.mp3quran.net/sds/069.mp3">سورة الحاقة</option>
            <option value="https://server11.mp3quran.net/sds/070.mp3">سورة المعارج</option>
            <option value="https://server11.mp3quran.net/sds/071.mp3">سورة نوح</option>
            <option value="https://server11.mp3quran.net/sds/072.mp3">سورة الجن</option>
            <option value="https://server11.mp3quran.net/sds/073.mp3">سورة المزمل</option>
            <option value="https://server11.mp3quran.net/sds/074.mp3">سورة المدثر</option>
            <option value="https://server11.mp3quran.net/sds/075.mp3">سورة القيامة</option>
            <option value="https://server11.mp3quran.net/sds/076.mp3">سورة الإنسان</option>
            <option value="https://server11.mp3quran.net/sds/077.mp3">سورة المرسلات</option>
            <option value="https://server11.mp3quran.net/sds/078.mp3">سورة النبأ</option>
            <option value="https://server11.mp3quran.net/sds/079.mp3">سورة النازعات</option>
            <option value="https://server11.mp3quran.net/sds/080.mp3">سورة عبس</option>
            <option value="https://server11.mp3quran.net/sds/081.mp3">سورة التكوير</option>
            <option value="https://server11.mp3quran.net/sds/082.mp3">سورة الإنفطار</option>
            <option value="https://server11.mp3quran.net/sds/083.mp3">سورة المطففين</option>
            <option value="https://server11.mp3quran.net/sds/084.mp3">سورة الإنشقاق</option>
            <option value="https://server11.mp3quran.net/sds/085.mp3">سورة البروج</option>
            <option value="https://server11.mp3quran.net/sds/086.mp3">سورة الطارق</option>
            <option value="https://server11.mp3quran.net/sds/087.mp3">سورة الأعلى</option>
            <option value="https://server11.mp3quran.net/sds/088.mp3">سورة الغاشية</option>
            <option value="https://server11.mp3quran.net/sds/089.mp3">سورة الفجر</option>
            <option value="https://server11.mp3quran.net/sds/090.mp3">سورة البلد</option>
            <option value="https://server11.mp3quran.net/sds/091.mp3">سورة الشمس</option>
            <option value="https://server11.mp3quran.net/sds/092.mp3">سورة الليل</option>
            <option value="https://server11.mp3quran.net/sds/093.mp3">سورة الضحى</option>
            <option value="https://server11.mp3quran.net/sds/094.mp3">سورة الشرح</option>
            <option value="https://server11.mp3quran.net/sds/095.mp3">سورة التين</option>
            <option value="https://server11.mp3quran.net/sds/096.mp3">سورة العلق</option>
            <option value="https://server11.mp3quran.net/sds/097.mp3">سورة القدر</option>
            <option value="https://server11.mp3quran.net/sds/098.mp3">سورة البينة</option>
            <option value="https://server11.mp3quran.net/sds/099.mp3">سورة الزلزلة</option>
            <option value="https://server11.mp3quran.net/sds/100.mp3">سورة العاديات</option>
            <option value="https://server11.mp3quran.net/sds/101.mp3">سورة القارعة</option>
            <option value="https://server11.mp3quran.net/sds/102.mp3">سورة التكاثر</option>
            <option value="https://server11.mp3quran.net/sds/103.mp3">سورة العصر</option>
            <option value="https://server11.mp3quran.net/sds/104.mp3">سورة الهمزة</option>
            <option value="https://server11.mp3quran.net/sds/105.mp3">سورة الفيل</option>
            <option value="https://server11.mp3quran.net/sds/106.mp3">سورة قريش</option>
            <option value="https://server11.mp3quran.net/sds/107.mp3">سورة الماعون</option>
            <option value="https://server11.mp3quran.net/sds/108.mp3">سورة الكوثر</option>
            <option value="https://server11.mp3quran.net/sds/109.mp3">سورة الكافرون</option>
            <option value="https://server11.mp3quran.net/sds/110.mp3">سورة النصر</option>
            <option value="https://server11.mp3quran.net/sds/111.mp3">سورة المسد</option>
            <option value="https://server11.mp3quran.net/sds/112.mp3">سورة الإخلاص</option>
            <option value="https://server11.mp3quran.net/sds/113.mp3">سورة الفلق</option>
            <option value="https://server11.mp3quran.net/sds/114.mp3">سورة الناس</option>
            
        </select>
        
        <audio id="quranPlayer" controls></audio>
        
        <!--<div class="audio-controls">
            <button onclick="document.getElementById('quranPlayer').play()">▶ تشغيل</button>
            <button onclick="document.getElementById('quranPlayer').pause()">⏸ إيقاف</button>
        </div>-->
    </div>
    <!-- نافذة البوب أب -->
    <div id="imsakiyahModal" class="modal-overlay">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal()">&times;</span>
            <table>
                <thead>
                    <tr>
                        <th>اليوم</th>
                        <th>التاريخ الهجري</th>
                        <th>الفجر</th>
                        <th>الشروق</th>
                        <th>الظهر</th>
                        <th>العصر</th>
                        <th>المغرب</th>
                        <th>العشاء</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- بيانات الإمساكية -->
                    <tr><td>1</td><td>1رمضان</td><td>04:30</td><td>05:45</td><td>12:15</td><td>15:30</td><td>18:25</td><td>19:40</td></tr>
                    <tr><td>2</td><td>2رمضان</td><td>04:29</td><td>05:44</td><td>12:15</td><td>15:30</td><td>18:26</td><td>19:41</td></tr>
                    <tr><td>3</td><td>3رمضان</td><td>04:28</td><td>05:43</td><td>12:15</td><td>15:30</td><td>18:27</td><td>19:42</td></tr>
                    <tr><td>4</td><td>4رمضان</td><td>04:27</td><td>05:42</td><td>12:15</td><td>15:30</td><td>18:28</td><td>19:43</td></tr>
                    <tr><td>5</td><td>5رمضان</td><td>04:26</td><td>05:41</td><td>12:15</td><td>15:30</td><td>18:29</td><td>19:44</td></tr>
                    <tr><td>6</td><td>6رمضان</td><td>04:25</td><td>05:40</td><td>12:15</td><td>15:30</td><td>18:30</td><td>19:45</td></tr>
                    <tr><td>7</td><td>7رمضان</td><td>04:24</td><td>05:39</td><td>12:15</td><td>15:30</td><td>18:31</td><td>19:46</td></tr>
                    <tr><td>8</td><td>8رمضان</td><td>04:23</td><td>05:38</td><td>12:15</td><td>15:30</td><td>18:32</td><td>19:47</td></tr>
                    <tr><td>9</td><td>9رمضان</td><td>04:22</td><td>05:37</td><td>12:15</td><td>15:30</td><td>18:33</td><td>19:48</td></tr>
                    <tr><td>10</td><td>10رمضان</td><td>04:21</td><td>05:36</td><td>12:15</td><td>15:30</td><td>18:34</td><td>19:49</td></tr>
                    <tr><td>11</td><td>11رمضان</td><td>04:20</td><td>05:35</td><td>12:15</td><td>15:30</td><td>18:35</td><td>19:50</td></tr>
                    <tr><td>12</td><td>12رمضان</td><td>04:19</td><td>05:34</td><td>12:15</td><td>15:30</td><td>18:36</td><td>19:51</td></tr>
                    <tr><td>13</td><td>13رمضان</td><td>04:18</td><td>05:33</td><td>12:15</td><td>15:30</td><td>18:37</td><td>19:52</td></tr>
                    <tr><td>14</td><td>14رمضان</td><td>04:17</td><td>05:32</td><td>12:15</td><td>15:30</td><td>18:38</td><td>19:53</td></tr>
                    <tr><td>15</td><td>15رمضان</td><td>04:16</td><td>05:31</td><td>12:15</td><td>15:30</td><td>18:39</td><td>19:54</td></tr>
                    <tr><td>16</td><td>16رمضان</td><td>04:15</td><td>05:30</td><td>12:15</td><td>15:30</td><td>18:40</td><td>19:55</td></tr>
                    <tr><td>17</td><td>17رمضان</td><td>04:14</td><td>05:29</td><td>12:15</td><td>15:30</td><td>18:41</td><td>19:56</td></tr>
                    <tr><td>18</td><td>18رمضان</td><td>04:13</td><td>05:28</td><td>12:15</td><td>15:30</td><td>18:42</td><td>19:57</td></tr>
                    <tr><td>19</td><td>19رمضان</td><td>04:12</td><td>05:27</td><td>12:15</td><td>15:30</td><td>18:43</td><td>19:58</td></tr>
                    <tr><td>20</td><td>20رمضان</td><td>04:11</td><td>05:26</td><td>12:15</td><td>15:30</td><td>18:44</td><td>19:59</td></tr>
                    <tr><td>21</td><td>21رمضان</td><td>04:10</td><td>05:25</td><td>12:15</td><td>15:30</td><td>18:45</td><td>20:00</td></tr>
                    <tr><td>22</td><td>22رمضان</td><td>04:09</td><td>05:24</td><td>12:15</td><td>15:30</td><td>18:46</td><td>20:01</td></tr>
                    <tr><td>23</td><td>23رمضان</td><td>04:08</td><td>05:23</td><td>12:15</td><td>15:30</td><td>18:47</td><td>20:02</td></tr>
                    <tr><td>24</td><td>24رمضان</td><td>04:07</td><td>05:22</td><td>12:15</td><td>15:30</td><td>18:48</td><td>20:03</td></tr>
                    <tr><td>25</td><td>25رمضان</td><td>04:06</td><td>05:21</td><td>12:15</td><td>15:30</td><td>18:49</td><td>20:04</td></tr>
                    <tr><td>26</td><td>26رمضان</td><td>04:05</td><td>05:20</td><td>12:15</td><td>15:30</td><td>18:50</td><td>20:05</td></tr>
                    <tr><td>27</td><td>27رمضان</td><td>04:04</td><td>05:19</td><td>12:15</td><td>15:30</td><td>18:51</td><td>20:06</td></tr>
                    <tr><td>28</td><td>28رمضان</td><td>04:03</td><td>05:18</td><td>12:15</td><td>15:30</td><td>18:52</td><td>20:07</td></tr>
                    <tr><td>29</td><td>29رمضان</td><td>04:02</td><td>05:17</td><td>12:15</td><td>15:30</td><td>18:53</td><td>20:08</td></tr>
                    <tr><td>30</td><td>30رمضان</td><td>04:01</td><td>05:16</td><td>12:15</td><td>15:30</td><td>18:54</td><td>20:09</td></tr>
    
                </tbody>
            </table>
        </div>



        <!-- الفوتر -->
        <footer>
            <p>رمضان 2025 | كل عام وأنتم بخير</p>
        </footer>
    </div>

    <script>
        // انتظر حتى يتم تحميل الصفحة بالكامل
        window.addEventListener('load', function() {
            // الحصول على عنصر الصوت
            const audio = document.getElementById('welcomeSound');

            // تشغيل الصوت تلقائيًا
            audio.play().catch(() => {
                // في حالة وجود خطأ (مثل قيود المتصفح على التشغيل التلقائي)
                console.log("التشغيل التلقائي غير مدعوم أو تم حظره.");
            });

            // إيقاف الصوت بعد انتهائه
            audio.addEventListener('ended', function() {
                audio.pause(); // إيقاف الصوت
                audio.currentTime = 0; // إعادة الصوت إلى البداية
            });
        });

        // التحكم في الراديو
        document.getElementById('surahSelector').addEventListener('change', function() {
            const player = document.getElementById('quranPlayer');
            player.pause();
            player.src = this.value;
            player.play().catch(() => {});
        });

        // تحسينات الأداء
        window.addEventListener('load', () => {
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                img.loading = "lazy";
                img.decoding = "async";
            });
        });
        // وظائف البوب أب
        function showImsakiyah() {
            document.getElementById('imsakiyahModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('imsakiyahModal').style.display = 'none';
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('imsakiyahModal');
            if (event.target == modal) {
                closeModal();
            }
        }
        // تحديث التاريخ والوقت
        function updateDateTime() {
            const date = new Date();
            
            const gregorianDate = date.toLocaleDateString('ar-EG', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            
            const hijriDate = new Intl.DateTimeFormat('ar-TN-u-ca-islamic', {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            }).format(date);
            
            const time = date.toLocaleTimeString('ar-EG');
            
            document.getElementById('gregorianDate').textContent = ` ${gregorianDate}`;
            document.getElementById('hijriDate').textContent = ` ${hijriDate}`;
            document.getElementById('liveClock').textContent = `🕒 ${time}`;
        }

        // تبديل جدول الإمساكية
        function toggleTable() {
            const table = document.getElementById('imsakiyahTable');
            const button = document.querySelector('.toggle-btn');
            table.classList.toggle('active');
            button.textContent = table.classList.contains('active') ? 'إخفاء الإمساكية ⏳' : 'إظهار إمساكية رمضان ⏳';
        }

        setInterval(updateDateTime, 1000);
        updateDateTime();
    </script>
</body>
</html>
