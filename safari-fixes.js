// safari-fixes.js - إصلاحات خاصة بمتصفح Safari

(function() {
    'use strict';

    // التحقق من Safari
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

    if (isSafari || isIOS) {
        console.log('Safari detected - applying Safari-specific fixes');
        
        // إصلاح مشكلة تحميل Google Maps في Safari
        window.safariMapFixes = {
            // معالجة أخطاء تحميل Google Maps
            handleMapLoadError: function() {
                const mapElement = document.getElementById('map');
                if (mapElement && !window.google) {
                    console.warn('Google Maps API not loaded, retrying...');
                    setTimeout(() => {
                        if (!window.google) {
                            this.showMapError();
                        }
                    }, 5000);
                }
            },

            // عرض رسالة خطأ للخريطة
            showMapError: function() {
                const mapElement = document.getElementById('map');
                if (mapElement) {
                    mapElement.innerHTML = `
                        <div style="padding: 20px; text-align: center; background: #f8f8f8; border: 1px solid #ddd; border-radius: 5px; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                            <div style="color: #d32f2f; font-size: 48px; margin-bottom: 20px;">⚠️</div>
                            <h3 style="color: #d32f2f; margin: 0 0 10px 0;">مشكلة في تحميل الخريطة</h3>
                            <p style="color: #666; margin: 10px 0; text-align: center;">
                                يبدو أن هناك مشكلة في تحميل خدمة الخرائط في Safari.<br>
                                يرجى المحاولة مرة أخرى أو استخدام متصفح آخر.
                            </p>
                            <div style="margin-top: 20px;">
                                <button onclick="location.reload()" style="padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">
                                    إعادة تحميل الصفحة
                                </button>
                                <button onclick="window.safariMapFixes.tryAlternativeMap()" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px;">
                                    جرب خريطة بديلة
                                </button>
                            </div>
                        </div>
                    `;
                }
            },

            // محاولة تحميل خريطة بديلة
            tryAlternativeMap: function() {
                const mapElement = document.getElementById('map');
                if (mapElement) {
                    mapElement.innerHTML = `
                        <div style="padding: 20px; text-align: center; background: #e3f2fd; border: 1px solid #2196f3; border-radius: 5px; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                            <div style="color: #1976d2; font-size: 48px; margin-bottom: 20px;">🗺️</div>
                            <h3 style="color: #1976d2; margin: 0 0 10px 0;">خريطة بديلة</h3>
                            <p style="color: #666; margin: 10px 0; text-align: center;">
                                يمكنك استخدام الروابط أدناه للوصول للمحطات:
                            </p>
                            <div style="margin-top: 20px;">
                                <a href="https://maps.google.com/maps?q=محطات+نور+السعودية" target="_blank" style="display: inline-block; padding: 10px 20px; background: #4285f4; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">
                                    فتح في خرائط Google
                                </a>
                                <a href="https://maps.apple.com/?q=محطات+نور+السعودية" target="_blank" style="display: inline-block; padding: 10px 20px; background: #007aff; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">
                                    فتح في خرائط Apple
                                </a>
                            </div>
                        </div>
                    `;
                }
            },

            // إصلاح مشاكل الموقع الجغرافي في Safari
            enhanceGeolocation: function() {
                if (navigator.geolocation) {
                    const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
                    
                    navigator.geolocation.getCurrentPosition = function(success, error, options) {
                        // إعدادات محسنة لـ Safari
                        const safariOptions = {
                            enableHighAccuracy: false,
                            timeout: 15000,
                            maximumAge: 300000,
                            ...options
                        };

                        // معالجة خاصة للأخطاء في Safari
                        const enhancedError = function(err) {
                            console.warn('Geolocation error in Safari:', err);
                            
                            if (err.code === err.PERMISSION_DENIED) {
                                alert('يرجى السماح بالوصول للموقع في إعدادات Safari:\nSafari > الإعدادات > الخصوصية والأمان > خدمات الموقع');
                            }
                            
                            if (error) error(err);
                        };

                        return originalGetCurrentPosition.call(this, success, enhancedError, safariOptions);
                    };
                }
            },

            // إصلاح مشاكل التمرير والتفاعل
            fixScrollingIssues: function() {
                // إصلاح مشاكل التمرير في Safari
                document.addEventListener('touchstart', function() {}, { passive: true });
                document.addEventListener('touchmove', function() {}, { passive: true });
                
                // إصلاح مشاكل الخريطة
                const mapContainer = document.querySelector('.map-container');
                if (mapContainer) {
                    mapContainer.style.webkitOverflowScrolling = 'touch';
                    mapContainer.style.webkitTransform = 'translateZ(0)';
                }
            },

            // تحسين الأداء العام
            optimizePerformance: function() {
                // تقليل استخدام الذاكرة
                if (window.performance && window.performance.memory) {
                    console.log('Memory usage:', window.performance.memory);
                }

                // تحسين الرسوم المتحركة
                document.documentElement.style.webkitBackfaceVisibility = 'hidden';
                document.documentElement.style.webkitPerspective = '1000px';
            },

            // تطبيق جميع الإصلاحات
            applyAllFixes: function() {
                this.enhanceGeolocation();
                this.fixScrollingIssues();
                this.optimizePerformance();
                this.handleMapLoadError();
                
                console.log('Safari fixes applied successfully');
            }
        };

        // تطبيق الإصلاحات عند تحميل الصفحة
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                window.safariMapFixes.applyAllFixes();
            });
        } else {
            window.safariMapFixes.applyAllFixes();
        }

        // معالجة أخطاء JavaScript العامة
        window.addEventListener('error', function(event) {
            console.error('JavaScript error in Safari:', event.error);
            
            if (event.error && event.error.message && event.error.message.includes('google')) {
                console.warn('Google Maps related error detected');
                setTimeout(() => {
                    window.safariMapFixes.handleMapLoadError();
                }, 1000);
            }
        });

        // معالجة أخطاء الشبكة
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection in Safari:', event.reason);
            
            if (event.reason && event.reason.toString().includes('fetch')) {
                console.warn('Network error detected, this might affect map loading');
            }
        });
    }
})();
