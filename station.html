<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="robots" content="max-image-preview:large">
    
    <link href="https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/dist/feather.min.css" rel="stylesheet">
    <link rel="icon" href="../favicon.png" type="image/x-icon" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="style.css" />
    
    <style>
        img:is([sizes="auto" i], [sizes^="auto," i]) {
            contain-intrinsic-size: 3000px 1500px;
        }
    </style>

    <style id="classic-theme-styles-inline-css" type="text/css">
        .wp-block-button__link {
            color: #fff;
            background-color: #32373c;
            border-radius: 9999px;
            box-shadow: none;
            text-decoration: none;
            padding: calc(.667em + 2px) calc(1.333em + 2px);
            font-size: 1.125em;
        }

        .wp-block-file__button {
            background: #32373c;
            color: #fff;
            text-decoration: none;
        }
    </style>

    <style id="wp-emoji-styles-inline-css" type="text/css">
        img.wp-smiley,
        img.emoji {
            display: inline !important;
            border: none !important;
            box-shadow: none !important;
            height: 1em !important;
            width: 1em !important;
            margin: 0 0.07em !important;
            vertical-align: -0.1em !important;
            background: none !important;
            padding: 0 !important;
        }
    </style>

    <script type="text/javascript">
        /* <![CDATA[ */
        window._wpemojiSettings = {
            "baseUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.0.3\/72x72\/",
            "ext": ".png",
            "svgUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.0.3\/svg\/",
            "svgExt": ".svg",
            "source": {
                "concatemoji": "https:\/\/noor.com.sa\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.7.2"
            }
        };
        /* ]]> */
    </script>

    <!-- External Scripts -->
    <script src="https://unpkg.com/@googlemaps/markerclusterer/dist/index.min.js"></script>
    <script src="https://unpkg.com/kdbush@3.0.0/kdbush.min.js"></script>
    <script src="https://unpkg.com/geokdbush@2.0.0/dist/geokdbush.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
    <script defer src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.0/xlsx.full.min.js"></script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAuoser5drR-xx8k9GzGz3d8FZNBY60_LE&libraries=geometry&callback=initMap" async defer></script>
    <script defer type="module" src="script.js"></script>
    <script src="map.js" defer></script>

</head>

<body class="body">

    <!-- Cursor Animation -->
    <div class="cursor-page-inner">
        <div class="circle-cursor circle-cursor-inner" style="visibility: visible; opacity: 1; transform: translate(1258px, 909px);"></div>
        <div class="circle-cursor circle-cursor-outer" style="visibility: visible; transform: translate(1258px, 909px);"></div>
    </div>

    <!-- Cookie Consent -->
    <div id="cookieConsent">
        <p>نستخدم ملفات تعريف الارتباط لتحسين تجربتك. <a href="/privacy">المزيد من المعلومات</a>.</p>
        <button id="acceptCookies">موافق</button>
    </div>

    <!-- Custom Alert -->
    <div id="customAlert" class="custom-alert">
        <div class="alert-content">
            <span class="close-btn" onclick="closeCustomAlert()">&times;</span>
            <h3 id="alertTitle"></h3>
            <p id="alertMessage"></p>
            <p class="welcome-message">نتشرف بتواجدك</p>
        </div>
    </div>

    <!-- Loading Message -->
    <div id="loadingMessage">
        <div class="loader">
            <svg class="loader-svg" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 47.94 47.94" style="enable-background:new 0 0 47.94 47.94;" xml:space="preserve">
                <path style="fill:#fff;" d="M26.285,2.486l5.407,10.956c0.376,0.762,1.103,1.29,1.944,1.412l12.091,1.757
                    c2.118,0.308,2.963,2.91,1.431,4.403l-8.749,8.528c-0.608,0.593-0.886,1.448-0.742,2.285l2.065,12.042
                    c0.362,2.109-1.852,3.717-3.746,2.722l-10.814-5.685c-0.752-0.395-1.651-0.395-2.403,0l-10.814,5.685
                    c-1.894,0.996-4.108-0.613-3.746-2.722l2.065-12.042c0.144-0.837-0.134-1.692-0.742-2.285l-8.749-8.528
                    c-1.532-1.494-0.687-4.096,1.431-4.403l12.091-1.757c0.841-0.122,1.568-0.65,1.944-1.412l5.407-10.956
                    C22.602,0.567,25.338,0.567,26.285,2.486z"></path>
            </svg>
        </div>
    </div>

    <!-- Sidebar Toggle -->
    <button id="toggleSidebar" class="toggle-sidebar" aria-label="فتح القائمة الجانبية">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-handle"></div>
        <ul>
            <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
            <li><a href="station.html"><i class="fas fa-gas-pump"></i> المحطات</a></li>
        </ul>
    </nav>

    <!-- Header -->
    <header class="header">
        <img src="../logo1.png" alt="شعار محطات نور خوي" class="logo" loading="lazy" />
        <div class="welcome d-flex align-items-center">
            مرحباً بكم فى محـطات نـــور 
            <img src="https://noor.com.sa/wp-content/themes/noor/images/apple-touch-icon-72x72.png" data-no-retina=""/>
        </div>

        <!--<div class="col-4 text-start d-none d-md-flex">
        <a href="tel:8003033313" class="widget fs-16 text-dark-gray fw-500 me-25px md-me-15px text-dark-gray">
            <img src="phone-call.svg" alt="Phone Icon" width="18" height="18" />
            <span class="dir-ltr">8003033313</span>
        </a>
        </div> -->
        <button id="languageToggle" onclick="toggleLanguage()" class="widget fs-16 text-dark-gray alt-font me-25px fw-500 d-none d-lg-inline-block">
            <i class="fa-thin fbs-globe me-5px"></i> <i class="fas fa-globe"></i> English

        </button>

    </header>

    <!-- Map -->
    <div class="map-container" id="map"></div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="select-container">
            <div>
                <label for="regionSelect"></label>
                <select id="regionSelect">
                    <option value="">اختر منطقة</option>
                </select>
            </div>
            <div>
                <label for="startLocation"></label>
                <select id="startLocation" disabled>
                    <option value="موقعي الحالي">موقعي الحالي</option>
                </select>
            </div>
            <div>
                <label for="endLocation"></label>
                <select id="endLocation" disabled>
                    <option value="موقعي الحالي">موقعي الحالي</option>
                </select>
            </div>
            <div>
                <button id="resetButton" onclick="resetAll()" aria-label="إعادة التعيين">إعادة التعيين</button>
            </div>
            <div>
                <button id="nearestStationButton" onclick="findNearestStation()" aria-label="عرض أقرب محطة">
                    عرض أقرب محطة
                </button>
            </div>
        </div>
    </div>

    <!-- Tables -->
    <div class="table">
        <div class="table-container">
            <h4 id="regionsTableTitle">المناطق والمواقع</h4>
            <table class="regions-table">
                <thead>
                    <tr>
                        <th>المنطقة</th>
                        <th>الموقع</th>
                        <th>المسافة (كم)</th>
                        <th>الخريطة</th>
                    </tr>
                </thead>
                <tbody id="regionsTable"></tbody>
            </table>
        </div>
        
        <div class="table-container">
            <h4 id="distanceTableTitle">حساب المسافة بين موقعين</h4>
            <table class="distance-table">
                <thead>
                    <tr>
                        <th>الموقع الأول</th>
                        <th>الموقع الثاني</th>
                        <th>المسافة (كم)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td id="selectedStart">---</td>
                        <td id="selectedEnd">---</td>
                        <td id="calculatedDistance">---</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <!-- Footer -->
    <footer class="footer">
        <p class="footer-title">حقوق النشر © <span>Noor Khoy</span> <span id="currentYear"></span></p>
    </footer>

</body>

</html>
