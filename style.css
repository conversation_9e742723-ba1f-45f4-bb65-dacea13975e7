@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* إعدادات عامة محسنة لـ Safari */
html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    padding-bottom: 40px;
    font-family: var(--primary-font), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    word-break: break-word;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    direction: rtl;
    background-image: url('./AboutNoor.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    /* تحسين Safari - تجنب background-attachment: fixed */
    background-attachment: scroll;
    /* إضافة دعم أفضل لـ Safari */
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    /* تحسين الأداء */
    will-change: auto;
    transform: translateZ(0);
}

/* إصلاحات خاصة بـ Safari */
@supports (-webkit-appearance: none) {
    html, body {
        /* تحسينات خاصة بـ Safari */
        -webkit-overflow-scrolling: touch;
        -webkit-transform: translateZ(0);
    }
}
.dir-ltr {
    direction: ltr;
    display: inline-block;
}
.header {
    display: flex;

    align-items: center;
    flex-direction: column;
    padding: 5px;
    justify-content: space-between; /* توزيع المحتويات على الجانبين */
    background-color: #bdb4bd97;
}

.header h1 {
    margin: 0;
}

/* تصغير زر الإغلاق */
.gm-ui-hover-effect {
    width: 20px !important;
    height: 20px !important;
    background-size: 20px 20px !important;
    background-color: #f10000 !important;
}

/* تصغير زر المساعدة */
.gm-ui-hover-effect > div {
    width: 16px !important;
    height: 16px !important;
}

/* ضبط شاشة المعلومات */
.gm-style-iw {
    max-width: 200px !important;
    padding: 10px !important;
    overflow: hidden !important;
}

.gm-style-iw div {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* تقليل حجم زر نوع الخريطة */
.gm-svpc {
    transform: scale(0.7);
    transform-origin: top left;
}

/* تقليل المسافات بين العناصر */
.gm-control-active {
    margin: 5px;
}

/* تخصيص لون الأزرار (اختياري) */
.gm-control-active img {
    max-width: 20px;
    max-height: 20px;
}

#languageToggle {
    position: fixed;
    top: 0px;
    right: 60px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 24px;
    z-index: 1001;
    transition: background-color 0.3s ease;
}

#languageToggle:hover {
    color: #00ff62a9;
}
/*------------------------*/
/* تنسيقات رسالة التحميل */
.loading-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}

.loader {
    width: 80px;
    height: 80px;
    animation: rotate 2s linear infinite;
}

.loader-svg {
    enable-background: new 0 0 47.94 47.94; /* إذا كانت ضرورية */
}

.loader-path {
    fill: #fff;
    stroke: none;
}


.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}
.directions-btn {
    background: #4285f4;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    margin: 9px 23%;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: background 0.3s;
}

.directions-btn:hover {
    background: #357abd;
}

.directions-btn i {
    font-size: 16px;
}
/*----------------------*/
p {
    display: block;
    text-align: center;
    margin-top: -1px;
    margin-bottom: 0px;
    font-weight: bold;
    font-size: 11px;
    margin-right: auto;
}

h3, h4 {
    text-align: center;
    margin-top: 0px;
    padding: 3px;
    margin-bottom: auto;
    color: #000000;
    font-weight: bold;
    font-size: calc(1.1rem + .5vw);
}

#regionsTableContainer {
    max-height: calc(100vh - 60px);
    overflow-y: auto;
    margin-bottom: 60px;
}

.logo {
    width: 40px;
    height: auto;
    position: absolute;
    top: 2px;
    left: 10px;
}

/* حاوية الخريطة محسنة لـ Safari */
.map-container {
    width: 700px;
    height: 700px;
    background-color: #e2e2e2;
    margin-top: 20px;
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    margin-bottom: 20px;
    margin-left: auto;
    margin-right: auto;
    /* تحسينات Safari */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    /* إصلاح مشاكل العرض في Safari */
    contain: layout style paint;
}

/* تحسينات للخريطة نفسها */
#map {
    width: 100%;
    height: 100%;
    position: relative;
    /* تحسينات Safari */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    /* منع مشاكل التمرير في Safari */
    -webkit-overflow-scrolling: touch;
    /* تحسين الأداء */
    will-change: transform;
}

/* إصلاحات خاصة بـ Safari للخريطة */
@supports (-webkit-appearance: none) {
    .map-container {
        /* تحسين العرض في Safari */
        -webkit-perspective: 1000px;
        perspective: 1000px;
    }

    #map {
        /* منع مشاكل الرسم في Safari */
        -webkit-transform-style: preserve-3d;
        transform-style: preserve-3d;
    }
}

/* معلومات المحطة */
.station-info {
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
    background-color: white;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
}

.station-info img {
    width: 50px;
    height: 50px;
    margin-right: 15px;
}

.station-info h3 {
    font-size: 18px;
    margin: 0;
    color: #333;
}

.station-info p {
    margin: 5px 0 0;
    color: #666;
    font-size: 14px;
}

/* حاوية المحتوى الرئيسي */
.main-content {
    margin: 20px;
}

/* حاوية القائمة */
.select-container {
    display: flex;
    flex-direction: row;
    gap: 10px;
    align-content: center;
    align-items: flex-end;
    justify-content: center;
    direction: rtl;
}

.select-container select {
    width: 150px;
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.select-container button {
    width: 150px;
    padding: 10px;
    font-size: 16px;
    font: 1em sans-serif;
    color: #ffffff;
    border-radius: 5px;
    margin-bottom: 10px;
    border: 1px solid #cccccc00;
    background-color:  #3a6cf4 
}

.select-container button:hover {
    background-color: #17a085;
}

.show-map-button {
    color: #fff;
    border: 2px solid #fff;
    background-color: #3a6cf4;
    text-decoration: none;
    font-size: 0.88em;
    font-weight: 7;
    display: inline-block;
    padding: 0.6em 0.775em;
    letter-spacing: 0px;
    border-radius: 18px;
    margin-bottom: 0px;
    transition: 0.7s ease;
}

.show-map-button:hover {
    background-color: #00a100;
    color: #000;
    transform: scale(1.1);
    font-size: 0.9em;
    font-weight: bold;
}

.custom-alert {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,0,0,0.2);
    z-index: 1000;
    text-align: center;
    width: 320px;
    font-family: 'Arial', sans-serif;
}

.alert-content {
    position: relative;
}

.close-btn {
    position: absolute;
    right: 15px;
    top: 10px;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.welcome-message {
    color: #4CAF50;
    font-weight: bold;
    margin-top: 15px;
    border-top: 1px solid #eee;
    padding-top: 10px;
    font-size: 18px;
}

.alert-body {
    margin: 15px 0;
    line-height: 1.6;
}

.technical-details {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-top: 10px;
    font-size: 0.9em;
    color: #6c757d;
}

.fa-info-circle {
    margin-left: 5px;
}
.fa-spin {
    margin-left: 10px;
}
/* الجداول */
/* الجداول */
.table-container {
    margin-top: 20px;
    font-weight: bold;
    color: #000000;
}

.table-container table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    text-align: center;
    direction: rtl;
}

.table-container th, 
.table-container td {
    padding: 6px;
    border: 1px solid #9c9797;
    background-color: #6d71707c;
    text-align: center;
    direction: rtl;
}

.table-container th {
    background-color: #f1f1f1;
    font-size: 16px;
}

.table-container td {
    font-size: 14px;
}

table {
    width: 100%;
    border-collapse: collapse;
    direction: rtl; /* للغة العربية */
}

td, th {
    border: 1px solid #ddd;
    padding: 3px;
    text-align: center;
    display: table-cell;
}

th {
    background-color: #f2f2f2;
}

/* تذييل الصفحة */
.footer {
    position: fixed;
    bottom: -16px;
    left: 0;
    right: 0;
    background-color: #56565b00;
    color: #fff;
    padding: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
}

.footer-title {
    font-weight: 600;
}

/* تنسيق القائمة الجانبية */
.sidebar {
    position: fixed;
    top: 13%;
    right: -95px;
    transform: translateY(-20%);
    background-color: rgba(0, 0, 0, 0.911);
    padding: 19px;
    border-radius: 24px 0px 0px 24px;
    box-shadow: 2px 2px 10px rgba(243, 241, 241, 0.5);
    z-index: 999;
    transition: right 0.3sease; /* إضافة انتقال سلس */
}

.sidebar.active {
    right: -22px;
    transition: right 0.3s ease;
}

.sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar ul li {
    margin-bottom: 15px;
}

.sidebar ul li a {
    color: white;
    text-decoration: none;
    font-size: 16px;
    display: flex;
    align-items: center;
    transition: color 0.3s ease;
}

.sidebar ul li a i {
    margin-right: 10px;
    font-size: 18px;
}

.sidebar ul li a:hover {
    color: orange;
}

.toggle-sidebar {
    position: fixed;
    top: 0px;
    right: 5px;
    background-color: rgb(243 234 234 / 0%);
    color: rgb(17, 16, 16);
    border: none;
    padding: 8px;
    border-radius: 9px;
    cursor: pointer;
    z-index: 1001;
    font-size: 17px;
    transition: background-color 0.3s ease;
}

.toggle-sidebar:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.sidebar.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 19%;
    height: 79%;
    border-radius: 54px 0px 0px 47px;
    background-color: rgba(0, 0, 0, 0.8);
    cursor: pointer;
}

.sidebar.active:hover::before {
    background-color: rgba(0, 0, 0, 0.9); /* تغيير اللون عند التمرير */
}
.sidebar-handle {
    position: fixed;
    content: '';
    top: 0;
    left: 0;
    width: 15px; /* عرض الجزء الظاهر */
    height: 100%;
    border-radius: 50px 0px 0px 40px;
    background-color: rgba(0, 0, 0, 0);
    z-index: 999;
    cursor: pointer;
}
/* استجابة للعرض الكبير */
@media (min-width: 992px) {
    html[dir=rtl] .map-wrapper {
        width: 95%;
        height: 95%;
        margin: 0 auto;
        margin-top: 0px;
        padding-right: 9rem;
    }
    .col-4 {
        flex-grow: 1; /* تأكد من أن العناصر تأخذ المساحة المتاحة */
    }
    header .header-top-bar .widget {
        display: inline-block;
    }
    .widget {
        display: flex;
        align-items: center;
    }
    
    .widget img {
        margin-right: 8px; /* المسافة بين الأيقونة والنص */
    }
    .col-4 {
        display: flex;
        align-items: center; /* تأكد من محاذاة العناصر بشكل رأسي */
        justify-content: flex-start; /* تأكد من محاذاة العناصر إلى اليسار */
    }
    /* تطبيق الأنميشن على الصورة */
    .welcome img {
        width: 25px;
        margin-right: 10px;
        -webkit-animation: rotation 8s infinite linear; /* خاصية متصفح WebKit */
        animation: rotation 8s infinite linear; /* خاصية الأنميشن القياسية */
    }
    
    /* ضبط تنسيق الصورة بشكل عام */
    img {
        max-width: 100%;
        height: auto;
    }
    .welcome {
        font-size: 25px;
        margin-bottom: 0;
        color: #23262e;
        letter-spacing: -1px;
    }
    html[dir=rtl] .select-container {
        display: flex;
        flex-direction: row;
        gap: 10px;
        align-content: center;
        align-items: flex-end;
        justify-content: center;
        direction: rtl;
        flex-wrap: wrap;
    }

    .map-container {
        height: 400px;
    }

    .main-content {
        margin: 0 auto;
        width: 280px;
        height: -50%;
        display: flex;
        flex-direction: row;
        gap: 6px;
        align-content: center;
        align-items: flex-end;
        justify-content: center;
        direction: rtl;
        flex-wrap: wrap;
    }

    .table-container {
        position: relative;
        top: 50%;
        left: 50%;
        right: -30%;
        transform: translate(-50%, 0%);

        flex-wrap: wrap;
        width: 80%;
        height: auto;
        align-items: center;
        justify-content: center;
    }

    .select-container {
        display: flex;
        flex-direction: row;
        gap: 5px;
        direction: rtl;
        margin-right: 884px;
        margin-top: -380px;
        margin-bottom: 79px;
        transform: translate(4%, 0%);
        flex-wrap: wrap;
        justify-content: flex-end;
    }
}

/* استجابة للجوالات */
@media (max-width: 768px) {
    .map-container {
        width: 95%;
        height: 100%;
        margin: 0 auto;
        height: 300px;
    }
    .welcome {
        font-size: 18px;
        margin-bottom: 8px;
        color: #23262e;
        letter-spacing: -1px;
    }
    h3 {
        text-align: center;
        margin-top: 0px;
        padding: 3px;
        margin-bottom: auto;
        font-size: 15px;
    }

    h4 {
        text-align: center;
        margin-top: 0px;
        padding: 3px;
        margin-bottom: auto;
        font-size: 20px;
        color: #000000;
        font-weight: bold;
    }
    #sidebar {
        width: 250px;
        height: 100%;
        position: fixed;
        top: 0;
        left: -250px; /* إخفاء القائمة خارج الشاشة */
        background-color: #333;
        transition: left 0.3s ease;
    }
    
    #sidebar.active {
        left: 0; /* إظهار القائمة عند التفعيل */
    }
    
    #sidebarToggle {
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 1000;
        cursor: pointer;
    }
    .select-container select,
    .select-container button {
/*        display: flex;*/
        align-content: center;
/*        justify-content: center;
        flex-direction: column;
        flex-wrap: wrap;
        align-items: center;*/
        direction: rtl;
        font-size: 15px;
    }

    .gm-style .gm-style-iw-c {
        position: absolute;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        overflow: hidden;
        top: 0;
        left: 0;
        -webkit-transform: translate3d(-50%,-100%,0);
        transform: translate3d(-50%,-100%,0);
        background-color: white;
        border-radius: 8px;
        padding: 5px;
        -webkit-box-shadow: 0 2px 7px 1px rgba(0,0,0,.3);
        box-shadow: 0 2px 7px 1px rgba(0,0,0,.3);
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
    }

    .gm-style img {
        max-width: 50%;
        margin-left: 50px;
    }

    .show-map-button {
        font-variant: JIS78;
        color: #fff;
        border: 2px solid #fff;
        background-color: #3a6cf4;
        text-decoration: none;
        font-size: 0.80em;
        font-weight: 7;
        display: inline-block;
        padding: 0.3em 0.765em;
        letter-spacing: 0px;
        border-radius: 16px;
        margin-bottom: 3px;
        transition: 0.7s ease;
    }

    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #56565b00;
        color: #fff;
        padding: 0px;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 999;
    }

    .show-map-button:hover {
        background-color: #00a100;
        color: #000;
        transform: scale(1.1);
        font-size: 0.9em;
        font-weight: bold;
    }
}

@media (max-width: 480px) {
    .map-container {
        height: 250px;
    }

    h3 {
        text-align: center;
        margin-top: 0px;
        padding: 3px;
        margin-bottom: auto;
        font-size: 15px;
    }

    h4 {
        text-align: center;
        margin-top: 0px;
        padding: 3px;
        margin-bottom: auto;
        font-size: 20px;
        color: #000000;
        font-weight: bold;
    }

    .select-container {
        display: flex;
        flex-direction: row;
        gap: 6px;
        align-content: center;
        align-items: flex-end;
        justify-content: center;
        direction: rtl;
        flex-wrap: wrap;
    }

    .gm-style .gm-style-iw-c {
        position: absolute;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        overflow: hidden;
        top: 0;
        left: 0;
        -webkit-transform: translate3d(-50%,-100%,0);
        transform: translate3d(-50%,-100%,0);
        background-color: white;
        border-radius: 8px;
        padding: 5px;
        -webkit-box-shadow: 0 2px 7px 1px rgba(0,0,0,.3);
        box-shadow: 0 2px 7px 1px rgba(0,0,0,.3);
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
    }

    .gm-style img {
        max-width: 50%;
        margin-left: 50px;
    }

    .show-map-button {
        color: #fff;
        border: 2px solid #fff;
        background-color: #3a6cf4;
        text-decoration: none;
        font-size: 0.91em;
        font-weight: 8;
        display: inline-block;
        padding: 0.4em 0.865em;
        letter-spacing: 1px;
        border-radius: 25px;
        margin-bottom: 5px;
        transition: 0.7s ease;
    }

    .show-map-button:hover {
        background-color: #00a100;
        color: #000;
        transform: scale(1.1);
        font-size: 0.9em;
        font-weight: bold;
    }
}

@media (max-width: 991.9px) {
    .header h1 {
        position: absolute;
        padding: 0px 60px;
        font-size: 18px;
    }

    .select-container {
        display: flex;
        flex-direction: row;
        gap: 6px;
        align-content: center;
        align-items: flex-end;
        justify-content: center;
        direction: rtl;
        flex-wrap: wrap;
    }

    .gm-style .gm-style-iw-c {
        position: absolute;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        overflow: hidden;
        top: 0;
        left: 0;
        -webkit-transform: translate3d(-50%,-100%,0);
        transform: translate3d(-50%,-100%,0);
        background-color: white;
        border-radius: 8px;
        padding: 5px;
        -webkit-box-shadow: 0 2px 7px 1px rgba(0,0,0,.3);
        box-shadow: 0 2px 7px 1px rgba(0,0,0,.3);
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
    }

    .gm-style img {
        max-width: 50%;
        margin-left: 50px;
    }
}

@media (max-width: 768px) {
    .header {
        padding: 0px;
    }
    header .header-top-bar .widget {
        display: inline-block;
    }
    .widget {
        display: flex;
        align-items: center;
    }
    
    .widget img {
        margin-right: 8px; /* المسافة بين الأيقونة والنص */
    }
    .col-4 {
        display: flex;
        align-items: center; /* تأكد من محاذاة العناصر بشكل رأسي */
        justify-content: flex-start; /* تأكد من محاذاة العناصر إلى اليسار */
    }
    /* تطبيق الأنميشن على الصورة */
    .welcome img {
        width: 25px;
        margin-right: 5px;
        -webkit-animation: rotation 8s infinite linear; /* خاصية متصفح WebKit */
        animation: rotation 8s infinite linear; /* خاصية الأنميشن القياسية */
    }
    
    /* ضبط تنسيق الصورة بشكل عام */
    img {
        max-width: 100%;
        height: auto;
    }
    .welcome {
        font-size: 25px;
        margin-bottom: 9px;
        color: #23262e;
        letter-spacing: -1px;
    }
    h3 {
        text-align: center;
        margin-top: auto;
        padding: 0px;
        margin-bottom: auto;
        font-size: 15px;
    }

    .h3, h3 {
        font-size: calc(1rem + .6vw);
    }

    h4 {
        text-align: center;
        margin-top: 0px;
        padding: 3px;
        margin-bottom: auto;
        font-size: 20px;
        color: #000000;
        font-weight: bold;
    }

    .logo {
        width: 40px;
        height: auto;
        position: absolute;
        top: 2px;
        left: 10px;
    }

    .gm-style .gm-style-iw-c {
        position: absolute;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        overflow: hidden;
        top: 0;
        left: 0;
        -webkit-transform: translate3d(-50%,-100%,0);
        transform: translate3d(-50%,-100%,0);
        background-color: white;
        border-radius: 8px;
        padding: 5px;
        -webkit-box-shadow: 0 2px 7px 1px rgba(0,0,0,.3);
        box-shadow: 0 2px 7px 1px rgba(0,0,0,.3);
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        flex-direction: column;
    }

    .gm-style .gm-style-iw-tc::after {
        background: #fff;
        -webkit-clip-path: polygon(0 0, 50% 100%, 100% 0);
        clip-path: polygon(0 0, 50% 100%, 100% 0);
        content: "";
        height: 22px;
        left: 0;
        position: absolute;
        top: -1px;
        width: 31px;
    }

    .gm-style img {
        max-width: 50%;
        margin-left: 50px;
    }

    .show-map-button {
        color: #fff;
        border: 2px solid #fff;
        background-color: #3a6cf4;
        text-decoration: none;
        font-size: 0.91em;
        font-weight: 8;
        display: inline-block;
        padding: 0.4em 0.865em;
        letter-spacing: 1px;
        border-radius: 25px;
        margin-bottom: 5px;
        transition: 0.7s ease;
    }

    .show-map-button:hover {
        background-color: #00a100;
        color: #000;
        transform: scale(1.1);
        font-size: 0.9em;
        font-weight: bold;
    }
}
/* تعريف الأنميشن مع البادئات المختلفة لتغطية المتصفحات المختلفة */
@-webkit-keyframes rotation {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes rotation {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* تنسيق صفحة الاتصال */
.contact-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 20px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
.a:hover {
    color: var(--base-color);
    transform: scale(1.05);
}
#cookieConsent {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    padding: 15px 25px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    display: none;
    align-items: center;
    gap: 15px;
    z-index: 1000;
    font-family: 'Arial', sans-serif;
    border: 1px solid #ddd;
}
.contact-form {
    flex: 1;
    margin-right: 20px;
}

.contact-info {
    flex: 1;
    margin-left: 20px;
}

.contact-form h2,
.contact-info h2 {
    font-size: 24px;
    color: #333;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 10px;
}

.form-group label {
    display: block;
    font-size: 16px;
    color: #555;
    margin-bottom: 5px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #fff;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #20c997;
    outline: none;
}

.submit-button {
    background-color: #20c997;
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.submit-button:hover {
    background-color: #17a085;
}

.contact-info ul {
    list-style: none;
    padding: 0;
}

.contact-info ul li {
    margin-bottom: 15px;
    font-size: 16px;
    color: #555;
}

.contact-info ul li i {
    margin-right: 10px;
    color: #20c997;
}

/* استجابة للجوالات */
@media (max-width: 768px) {
    .contact-container {
        flex-direction: column;
    }

    .contact-form,
    .contact-info {
        margin: 0;
        margin-bottom: 20px;
    }
}
/* أنماط تحميل متقدمة */
#loadingMessage {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0);
    padding: 25px 40px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.034);
    z-index: 1000;
    display: none;
    font-family: 'Arial', sans-serif;
    color: #2c3e50;
    text-align: center;
    /*border: 1px solid #eeeeee00;*/
  }
  
  .loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #da4500;
    border-radius: 50%;
    animation: spin 1.2s linear infinite;
    margin-bottom: 15px;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  #loadingMessage div {
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0.5px;
  }
  /* From Uiverse.io by AbanoubMagdy1 */ 
.loader {
    --dim: 6rem;
    background-color: #ED8A19;
    width: var(--dim);
    height: var(--dim);
    border-radius: 50%;
    display: grid;
    place-items: center;
    animation: spin_412 5s infinite;
  }
  
  .loader .loader-svg {
    transform: translateY(-2px) scale(.7);
  }
  
  @keyframes spin_412 {
    0% {
      transform: rotate(0) scale(1);
    }
  
    50% {
      transform: rotate(720deg) scale(1.3);
    }
  
    100% {
      transform: rotate(0) scale(1);
    }
  }
  /*.data-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: none;
    z-index: 1000;
}*/
.error-message {
    color: #dc3545;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    display: none;
}

.show-error {
    display: block;
}
