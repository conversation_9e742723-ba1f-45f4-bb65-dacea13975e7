* {
    padding: 0;
/*    margin: 0;*/
    box-sizing: border-box;
    font-family: 'Poppins';
}
html {
    scroll-behavior: smooth;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#favicon {
    animation: spin 5s linear infinite; /* تعديل سرعة الدوران هنا */
}

body {
    display: flex;
    flex-direction: column;
    height: 100vh; 
    margin: 0; 
    overflow: hidden; /* منع التمرير */
}

/* تحسين تصميم الأزرار */
.auth-buttons {
    position: absolute;
    top: 20px;  /* تحديد المسافة من الأعلى */
    left: 50%;
    transform: translateX(-50%); /* توسيط الأزرار أفقياً */
    z-index: 10; /* وضع الأزرار فوق المحتوى */
}

.auth-buttons .btn {
    background-color: #4caf4f1b;
    color: rgb(182, 182, 182);
    padding: 15px 30px;
    font-size: 16px;
    margin: 0 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
    background-color: transparent;
    text-decoration: none;
    letter-spacing: 1px;
    display: inline-block;
    border: 2px solid #bb0475;
    padding: 0.9375em 2.1875em;
    border-radius: 50px;
    transition: 0.9s ease; 
    box-shadow: #000 0px 0px 10px 0px;
    font-weight: bold; /* جعل الخط غامق */
}

.auth-buttons .btn:hover {
    background-color: #45a049;
    transform: scale(1.05);
}

.auth-buttons .btn:active {
    transform: scale(0.98);
}

/* تحسين تنسيق النماذج */
.form-container {
    background-color: rgba(255, 255, 255, 0.8); 
    padding: 30px;
    border-radius: 10px;
    width: 300px;
    margin: 20px auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: none; /* إخفاء النماذج في البداية */
}

/* تخصيص النماذج بشكل خاص */
#loginFormContainer,
#signupFormContainer {
    width: 300px;
    margin-top: 80px;
}

.form-container h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
}

.form-container input {
    width: 100%;
    padding: 10px;
    margin: 10px 0;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.form-container button {
    width: 100%;
    padding: 12px;
    background-color: #4caf4fe1;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.form-container button:hover {
    background-color: #45a049;
}

.logo {
    position: fixed;
    top: 8px;
    left: 10px;
    max-width: 40px;
    height: auto;
    z-index: 999;
}

.container {
    flex: 1; 
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden; /* منع التمرير داخل الحاوية */
}

.content {
    text-align: center;
    position: relative; 
    z-index: 1; 
    max-width: 90%; /* تجنب الحواف القريبة جداً */
}

.content h1 {
    font-size: 90px;
    color: #de560d;
    margin-bottom: 45px;
    margin-top: -152px;
    transition: top 0.5s, opacity 0.5s; 
    text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.5); /* إضافة ظل للنص */
}

.main-btn {
    font-size: 18px; 
    color: #de560d;
    background-color: transparent;
    text-decoration: none;
    letter-spacing: 1px;
    display: inline-block;
    border: 2px solid #bb0475;
    padding: 0.9375em 2.1875em;
    border-radius: 50px;
    transition: 0.9s ease; 
    box-shadow: #000 0px 0px 10px 0px;
    font-weight: bold; /* جعل الخط غامق */
}

.main-btn:hover {
    background-color: #1bad0d;
    color: #000;
    transform: scale(1.05); 
}

.download-buttons {
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    margin: 4px 0;
}

.download-buttons .btn {
    margin: 0 10px;
    display: flex;
    font-size: 16px;
    color: #000000d1;
    text-decoration: none;
    letter-spacing: 1px;
    border: 1px solid #aeaaaa;
    padding: 0.9375em; 
    min-width: 150px;
    border-radius: 50px;
    transition: 0.9sease;
    background-color: transparent;
    justify-content: space-evenly;
    align-items: center;
    align-content: center;
    flex-wrap: nowrap;
    flex-direction: column;
    box-shadow: #000 0px 0px 10px 0px;
    /*font-weight: bold;  جعل الخط غامق */
}

.download-buttons a:hover .fab.fa-apple {
    color: #535151; /* اللون الأصلي اب ستور */
}

.download-buttons a:hover .fab.fa-google-play {
    color:  #34A853; /* اللون الأصلي لجوجل بلاي */
}

.btn:hover {
    transform: scale(1.1);
    background-color: #000000;
    color: #f5f9f6;
}

video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
    opacity: 0.8;
}

.background-clip {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.phone-link {
    color: orange; /* لون برتقالي */
    text-decoration: none; /* إزالة الخط التحتي */
    font-weight: bold; /* جعل النص عريضًا */
}

.phone-link:hover {
    color: darkorange; /* تغيير اللون عند التمرير */
}

/* تنسيق القائمة الجانبية */
.sidebar {
    position: fixed;
    top: 19%;
    right: -75px;
    transform: translateY(-20%);
    background-color: rgba(0, 0, 0, 0.911);
    padding: 18px;
    border-radius: 24px 0px 0px 24px;
    box-shadow: 2px 2px 10px rgba(243, 241, 241, 0.5);
    z-index: 999;
    transition: right 0.3s ease; /* إضافة انتقال سلس */
}

.sidebar.active {
    right: 0;
    transition: right 0.3s ease;
}

.sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar ul li {
    margin-bottom: 15px;
}

.sidebar ul li a {
    color: white;
    text-decoration: none;
    font-size: 16px;
    display: flex;
    align-items: center;
    transition: color 0.3s ease;
}

.sidebar ul li a i {
    margin-right: 10px;
    font-size: 18px;
}

.sidebar ul li a:hover {
    color: orange;
}

.toggle-sidebar {
    position: fixed;
    top: 0px;
    right: 5px;
    background-color: rgb(243 234 234 / 0%);
    color: rgb(17, 16, 16);
    border: none;
    padding: 8px;
    border-radius: 9px;
    cursor: pointer;
    z-index: 1001;
    font-size: 17px;
    transition: background-color 0.3s ease;
}

.toggle-sidebar:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.sidebar.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 17%;
    height: 100%;
    border-radius: 54px 0px 0px 47px;
    background-color: rgba(0, 0, 0, 0.8);
    cursor: pointer;
}

.sidebar.active:hover::before {
    background-color: rgba(0, 0, 0, 0.9); /* تغيير اللون عند التمرير */
}
.social-icons {
    display: flex;
    justify-content: center;
    margin-top: 20px; 
    padding: 15px;
    border-radius: 50px;
}

.social-icons a {
    color: #020202;
    font-size: 1.7em; 
    margin: 0 15px; 
    transition: color 0.3s;
}

.social-icons a:hover {
    color: transparent; 
}
/*------------------------------------------*/
.social-icons a {
    position: relative;
    background: #ffffff15;
    border-radius: 50%;
    margin: 5px;
    width: 50px;
    height: 50px;
    /*font-size: 18px;*/
    text-decoration: none; /* إلغاء الخط التحتي */
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    box-shadow: 0 10px 10px rgba(0, 0, 0, 0);
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* التلميحات المنبثقة (tooltip) */
.social-icons a .tooltip {
    position: absolute;
    top: 0px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 14px;
    background: #3703f5fa;
    color: #ffffffe8;
    padding: 5px 8px;
    border-radius: 5px;
    box-shadow: 0 10px 10px rgba(0, 0, 0, 0.164);
    opacity: 0;
    pointer-events: none;
    pointer-events: auto;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.social-icons a .tooltip::before {
    position: absolute;
    content: "";
    height: 10px;
    width: 10px;
    background: #ffffff00;
    bottom: -3px;
    left: 50%;
    transform: translate(-50%) rotate(45deg);
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.social-icons a:hover .tooltip {
    top: -45px;
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}



/* تخصيص الألوان عند التمرير لكل أيقونة */

.social-icons .facebook:hover .tooltip,
.social-icons .facebook:hover .tooltip::before {
    background: #1877f2;
    color: #fff;
}


.social-icons .twitter:hover .tooltip,
.social-icons .twitter:hover .tooltip::before {
    background: #1da1f2;
    color: #fff;
}


.social-icons .instagram:hover .tooltip,
.social-icons .instagram:hover .tooltip::before {
    background: #e4405f;
    color: #fff;
}


.social-icons .youtube:hover .tooltip,
.social-icons .youtube:hover .tooltip::before {
    background: #ff0000;
    color: #fff;
}

/*.social-icons .wabsite:hover,*/
.social-icons .website:hover .tooltip,
.social-icons .website:hover .tooltip::before {
    background: #0004d463;
    color: #f05d07;
}


.social-icons .whatsapp:hover .tooltip,
.social-icons .whatsapp:hover .tooltip::before {
    background: #25d366;
    color: #fff;
}


.social-icons .snapchat:hover .tooltip,
.social-icons .snapchat:hover .tooltip::before {
    background: #fffc00;
    color: #000000;
}
.footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #000016ea;
    color: #e2e2e2;
    padding: 15px;
    display: flex;
    align-items: center;
    height: 30px;
    z-index: 999;
    justify-content: space-around;
}

.footer-title {
    font-size: 1em; 
    font-weight: 600;
}

.footer .social-icons {
    display: flex;
    gap: 15px; 
}

.footer .social-icons a {
    font-size: 1em; 
    color: #fff; 
}

.footer .social-icons a:hover {
    color: #ee2f0e; 
}

/* تغيير لون الأيقونات عند التمرير */
.social-icons a:hover {
    color: transparent; /* اجعل اللون الأصلي غير مرئي */
}

/* تحديد الألوان الأصلية لكل أيقونة */
.social-icons a:hover .fas.fa-globe {
    color: #eb7210; /* ويب */
}
.social-icons a:hover .fab.fa-whatsapp {
    color: #00b944; /* واتساب */
}

.social-icons a:hover .fab.fa-facebook {
    color: #0138af; /* فيسبوك */
}

.social-icons a:hover .fab.fa-twitter {
    color: #1DA1F2; /* تويتر */
}

.social-icons a:hover .fab.fa-snapchat {
    color: #FFFC00; /* سناب شات */
}

.social-icons a:hover .fab.fa-instagram {
    color: #C13584; /* إنستغرام */
}

.social-icons a:hover .fab.fa-youtube {
    color: #FF0000; /* يوتيوب */
}

#loadingMessage {
    font-size: 18px;
    color: #333;
    text-align: center;
    margin-bottom: 20px;
}

.survey-button {
    text-align: center;
    margin-top: 20px;
}

.survey-button .btn {
    background-color: #4CAF50; /* اللون الأخضر */
    color: white;
    padding: 10px 20px;
    text-decoration: none;
    font-size: 16px;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.survey-button .btn:hover {
    background-color: #45a049; /* تغير اللون عند التمرير */
}

@media (max-width: 641px) {
    .download-buttons {
        display: flex;
        justify-content: center;
        position: absolute;
        bottom: 80px;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        margin: 24px 0;
    }
    video {
        height: 100vh; /* تأكد من ملء ارتفاع الشاشة بالكامل على الجوال */
        width: 100%; /* تأكد من ملء عرض الشاشة بالكامل */
    }
    .content h1 {
        font-size: 50px; /* حجم النص على الشاشات الصغيرة */
        margin-bottom: 30px; /* تقليل المسافة السفلية */
    }
    .main-btn {
        font-size: 18px; /* حجم الزر على الشاشات الصغيرة */
        padding: 0.75em 1.5em; /* تقليل الحشوة */
    }
    .download-buttons .btn {
        font-size: 12px; /* حجم الخط للأزرار */
        min-width: 120px; /* تقليل العرض الأدنى للأزرار */
    }
    .social-icons a {
        font-size: 1.5em; /* حجم الأيقونات */
    }
    .footer {
        padding: 10px; /* تقليل الحشوة في الفوتر */
    }
    .footer-title {
        font-size: 0.9em; /* تقليل حجم النص في الفوتر */
    }
}

@media (max-width: 300px) {
    .download-buttons {
        display: flex;
        justify-content: center;
        position: absolute;
        bottom: 80px;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        margin: 24px 0;
    }
    .content h1 {
        font-size: 40px; /* حجم النص على الشاشات الأصغر */
    }
    .main-btn {
        font-size: 16px; /* حجم الزر على الشاشات الأصغر */
    }
}

/* الألوان عند فتح الموقع على الهاتف */
@media (max-width: 641px) {
    .download-buttons {
        display: flex;
        justify-content: center;
        position: absolute;
        bottom: 80px;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        margin: 24px 0;
    }
    .social-icons a .fas.fa-globe {
        color: #eb7210; /* ويب */
    }
    .social-icons a .fab.fa-whatsapp {
        color: #00b944; /* واتساب */
    }
    .social-icons a .fab.fa-facebook {
        color: #0138af; /* فيسبوك */
    }
    .social-icons a .fab.fa-twitter {
        color: #1DA1F2; /* تويتر */
    }
    .social-icons a .fab.fa-snapchat {
        color: #FFFC00; /* سناب شات */
    }
    .social-icons a .fab.fa-instagram {
        color: #C13584; /* إنستغرام */
    }
    .social-icons a .fab.fa-youtube {
        color: #FF0000; /* يوتيوب */
    }
}

/*.chat-container {
    width: 50%;
    max-width: 250px;
    margin: 20px auto;
    border: 1px solid #b9b9b9a1;
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background-color: #ffffff00;
}

.messages {
    flex: 1;
    padding: 10px;
    overflow-y: auto;
    height: 400px;
    background: #f9f9f9;
}

.message {
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 10px;
}

.message.user1 {
    background-color: #d1ecf1;
    text-align: left;
}

.message.user2 {
    background-color: #f8d7da;
    text-align: right;
}

.input-container {
    display: flex;
    border-top: 1px solid #ddd;
}

.input-container input {
    flex: 1;
    padding: 10px;
    border: none;
    outline: none;
}

.input-container button {
    padding: 10px;
    background: #007bff;
    color: white;
    border: none;
    cursor: pointer;
}*/

/* تعديل على الزر "رأيكم يهمنا" */
.survey-button {
    margin-top: 20px;
}

.survey-button .btn {
    background-color: #4CAF50;
    color: white;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 30px;
    font-weight: bold;
}

.survey-button .btn:hover {
    background-color: #45a049;
}
