<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Safari - محطات نور</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a8b;
        }
        #map-test {
            width: 100%;
            height: 300px;
            background: #e0e0e0;
            border: 1px solid #ccc;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>اختبار Safari - محطات نور</h1>
    
    <div class="test-container">
        <h2>معلومات المتصفح</h2>
        <div id="browser-info"></div>
    </div>

    <div class="test-container">
        <h2>اختبار JavaScript</h2>
        <button onclick="testJavaScript()">اختبار JavaScript</button>
        <div id="js-result"></div>
    </div>

    <div class="test-container">
        <h2>اختبار الموقع الجغرافي</h2>
        <button onclick="testGeolocation()">اختبار الموقع الجغرافي</button>
        <div id="geo-result"></div>
    </div>

    <div class="test-container">
        <h2>اختبار تحميل Google Maps</h2>
        <button onclick="testGoogleMaps()">اختبار Google Maps</button>
        <div id="maps-result"></div>
        <div id="map-test">انقر على الزر أعلاه لاختبار الخريطة</div>
    </div>

    <div class="test-container">
        <h2>اختبار تحميل البيانات</h2>
        <button onclick="testDataLoading()">اختبار تحميل البيانات</button>
        <div id="data-result"></div>
    </div>

    <div class="test-container">
        <h2>الإجراءات</h2>
        <button onclick="window.location.href='station.html'">الذهاب للموقع الرئيسي</button>
        <button onclick="location.reload()">إعادة تحميل الاختبار</button>
    </div>

    <script>
        // عرض معلومات المتصفح
        document.getElementById('browser-info').innerHTML = `
            <div class="test-result ${navigator.userAgent.includes('Safari') ? 'success' : 'warning'}">
                <strong>المتصفح:</strong> ${navigator.userAgent}<br>
                <strong>Safari:</strong> ${navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome') ? 'نعم' : 'لا'}<br>
                <strong>iOS:</strong> ${/iPad|iPhone|iPod/.test(navigator.userAgent) ? 'نعم' : 'لا'}<br>
                <strong>اللغة:</strong> ${navigator.language}<br>
                <strong>Platform:</strong> ${navigator.platform}
            </div>
        `;

        function testJavaScript() {
            const result = document.getElementById('js-result');
            try {
                // اختبار ميزات JavaScript الأساسية
                const testArray = [1, 2, 3];
                const testObject = { test: 'value' };
                const testPromise = Promise.resolve('success');
                
                result.innerHTML = '<div class="test-result success">✅ JavaScript يعمل بشكل صحيح</div>';
            } catch (error) {
                result.innerHTML = `<div class="test-result error">❌ خطأ في JavaScript: ${error.message}</div>`;
            }
        }

        function testGeolocation() {
            const result = document.getElementById('geo-result');
            
            if (!navigator.geolocation) {
                result.innerHTML = '<div class="test-result error">❌ الموقع الجغرافي غير مدعوم</div>';
                return;
            }

            result.innerHTML = '<div class="test-result warning">⏳ جاري اختبار الموقع الجغرافي...</div>';

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    result.innerHTML = `
                        <div class="test-result success">
                            ✅ تم الحصول على الموقع بنجاح<br>
                            <strong>خط العرض:</strong> ${position.coords.latitude}<br>
                            <strong>خط الطول:</strong> ${position.coords.longitude}<br>
                            <strong>الدقة:</strong> ${position.coords.accuracy} متر
                        </div>
                    `;
                },
                function(error) {
                    let errorMsg = '';
                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMsg = 'تم رفض الإذن للوصول للموقع';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMsg = 'معلومات الموقع غير متاحة';
                            break;
                        case error.TIMEOUT:
                            errorMsg = 'انتهت مهلة طلب الموقع';
                            break;
                        default:
                            errorMsg = 'خطأ غير معروف';
                            break;
                    }
                    result.innerHTML = `<div class="test-result error">❌ ${errorMsg}: ${error.message}</div>`;
                },
                {
                    enableHighAccuracy: false,
                    timeout: 10000,
                    maximumAge: 60000
                }
            );
        }

        function testGoogleMaps() {
            const result = document.getElementById('maps-result');
            const mapDiv = document.getElementById('map-test');
            
            result.innerHTML = '<div class="test-result warning">⏳ جاري تحميل Google Maps...</div>';

            // تحميل Google Maps API
            if (typeof google !== 'undefined' && google.maps) {
                initTestMap();
            } else {
                const script = document.createElement('script');
                script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyAuoser5drR-xx8k9GzGz3d8FZNBY60_LE&callback=initTestMap';
                script.onerror = function() {
                    result.innerHTML = '<div class="test-result error">❌ فشل في تحميل Google Maps API</div>';
                };
                document.head.appendChild(script);
            }

            window.initTestMap = function() {
                try {
                    const map = new google.maps.Map(mapDiv, {
                        center: { lat: 24.7136, lng: 46.6753 },
                        zoom: 10
                    });
                    
                    result.innerHTML = '<div class="test-result success">✅ تم تحميل Google Maps بنجاح</div>';
                } catch (error) {
                    result.innerHTML = `<div class="test-result error">❌ خطأ في إنشاء الخريطة: ${error.message}</div>`;
                }
            };
        }

        function testDataLoading() {
            const result = document.getElementById('data-result');
            
            result.innerHTML = '<div class="test-result warning">⏳ جاري اختبار تحميل البيانات...</div>';

            // اختبار تحميل ملف البيانات
            fetch('data.xlsx')
                .then(response => {
                    if (response.ok) {
                        result.innerHTML = '<div class="test-result success">✅ تم تحميل ملف البيانات بنجاح</div>';
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .catch(error => {
                    // اختبار Google Sheets API كبديل
                    const SHEET_ID = '1NItFn79G46YMoqdyeBvGO-oGiPqfR-oXzHEi1i-OxHM';
                    const API_KEY = 'AIzaSyAL2-JNGE6oTtgVWaQH2laI45G__2nop-8';
                    const SHEET_NAME = 'stations';
                    
                    fetch(`https://sheets.googleapis.com/v4/spreadsheets/${SHEET_ID}/values/${SHEET_NAME}?key=${API_KEY}`)
                        .then(response => {
                            if (response.ok) {
                                return response.json();
                            } else {
                                throw new Error(`HTTP ${response.status}`);
                            }
                        })
                        .then(data => {
                            result.innerHTML = `<div class="test-result success">✅ تم تحميل البيانات من Google Sheets (${data.values ? data.values.length : 0} صف)</div>`;
                        })
                        .catch(sheetsError => {
                            result.innerHTML = `<div class="test-result error">❌ فشل في تحميل البيانات: ${error.message} | ${sheetsError.message}</div>`;
                        });
                });
        }

        // تشغيل اختبارات أساسية عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            testJavaScript();
        });
    </script>
</body>
</html>
